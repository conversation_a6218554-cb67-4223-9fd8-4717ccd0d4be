{"name": "nvision-data-collection", "version": "4.6.1", "description": "Nvision Data Collection App", "author": "Nvision", "main": "main.js", "scripts": {"dev": "concurrently \"vite\" \"npm:dev-electron\"", "dev-electron": "wait-on http://localhost:5173 && cross-env NODE_ENV=development electron .", "version:major": "node scripts/version-bump.js major", "version:minor": "node scripts/version-bump.js minor", "version:patch": "node scripts/version-bump.js patch", "build:major": "npm run version:major && npm run build", "build:minor": "npm run version:minor && npm run build", "build:patch": "npm run version:patch && npm run build", "build": "vite build && electron-builder", "start": "cross-env NODE_ENV=production electron ."}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/react": "^9.22.0", "@sentry/vite-plugin": "^3.5.0", "@tailwindcss/vite": "^4.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-squirrel-startup": "^1.0.0", "jimp": "^0.16.1", "lucide-react": "^0.511.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^29.0.1", "electron-builder": "^26.0.12", "electronmon": "^2.0.3", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^5.1.4", "vite-plugin-node-polyfills": "^0.23.0", "wait-on": "^8.0.3"}, "build": {"appId": "com.nvision.datacollection", "productName": "Nvision AI", "publish": null, "directories": {"buildResources": "src/assets", "output": "dist_electron/${version}"}, "files": ["dist/**/*", "main.js", "preload.js", "package.json", "src/assets/**/*"], "icon": "nvision_logo.ico", "win": {"target": "nsis", "artifactName": "${productName}-${version}.${ext}"}}}
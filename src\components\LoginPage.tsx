import React, { useState } from 'react';
import * as Sentry from '@sentry/react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import nvision_logo from '../assets/nvision_logo.png';
import { login } from '@/services/api';

interface LoginPageProps {
  onLogin: (token: string) => void;
  // navigateToSignup: () => void;
}

type LoginFormProps = LoginPageProps & React.ComponentPropsWithoutRef<'div'>;

export function LoginPage({
  onLogin,
  // navigateToSignup,
  className,
  ...props
}: LoginFormProps) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    try {
      const data = await login(username, password);
      localStorage.setItem('sentinel_dash_username', username);
      localStorage.setItem('sentinel_dash_refresh', data.refresh);
      onLogin(data.access);
    } catch (error: any) {
      setError(error.message || 'Invalid username or password');
      Sentry.captureException(error, {
        tags: {
          location: 'LoginPage',
          operation: 'user_login',
        },
        extra: {
          username: username,
          errorStatus: error.response?.status,
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="grid h-screen pt-8 lg:grid-cols-2">
      <div className="relative hidden lg:block bg-[#6AA526]"></div>
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">
          <a href="#" className="flex items-center gap-2 font-medium">
            <div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
              <img
                src={nvision_logo}
                className="size-6"
                alt="Nvision AI Logo"
              ></img>{' '}
            </div>
            Nvision AI
          </a>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-xs">
            <form
              className={cn('flex flex-col gap-6', className)}
              {...props}
              onSubmit={handleSubmit}
            >
              <div className="flex flex-col items-center gap-2 text-center">
                <h1 className="text-2xl font-bold">Login to your account</h1>
                <p className="text-balance text-sm text-muted-foreground">
                  Enter your credentials below to login to your account
                </p>
              </div>
              <div className="grid gap-6">
                <div className="grid gap-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    type="username"
                    // placeholder="Alex"
                    required
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
                <div className="grid gap-2">
                  <div className="flex items-center">
                    <Label htmlFor="password">Password</Label>
                    {/* <a
                      href="#"
                      className="ml-auto text-sm underline-offset-4 hover:underline"
                    >
                      Forgot your password?
                    </a> */}
                  </div>
                  <Input
                    id="password"
                    type="password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
                {error && (
                  <div className="text-red-500 text-sm text-center">
                    {error}
                  </div>
                )}
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? 'Logging in...' : 'Login'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
